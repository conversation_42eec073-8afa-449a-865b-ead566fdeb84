I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The user wants to implement the core foundation interfaces and classes for the MCP SDD server. Based on the detailed specifications in `PHASE_0_PREPARATION.md` and `ARCHITECTURE.md`, I need to create critical interfaces (`IStateRepository`, `IWorkflowManager`, `IMCPToolHandler`), implement the `SDDPhase` enum, set up strict TypeScript configuration, and create a basic `MCPServer` class with JSON-RPC 2.0 protocol handling. The specifications provide exact implementations that need to be translated into the established project structure.

### Approach

I'll implement the core foundation by creating the critical interfaces in separate files within `src/core/interfaces/`, defining the `SDDPhase` enum and supporting types in `src/core/types/`, implementing the `MCPServer` class with JSON-RPC 2.0 handling in `src/core/server/`, and configuring TypeScript with strict settings along with <PERSON><PERSON><PERSON>/Prettier. The approach follows the exact specifications from the preparation document while organizing code into logical modules for maintainability. Each interface will be in its own file to avoid circular dependencies, and the MCPServer will use dependency injection for testability.

### Reasoning

I analyzed the user's requirements and read the detailed specifications in `PHASE_0_PREPARATION.md` and `ARCHITECTURE.md`. These documents contain exact TypeScript implementations for all the required interfaces, enums, and classes. I also reviewed the project structure that was previously established to understand where these files should be placed within the `mcp-sdd-server` directory structure.

## Mermaid Diagram

sequenceDiagram
    participant Client as MCP Client
    participant Server as MCPServer
    participant StateRepo as IStateRepository
    participant WorkflowMgr as IWorkflowManager
    participant Tool as IMCPToolHandler

    Note over Client,Tool: Core Interface Implementation

    Client->>Server: JSON-RPC Request (tools/list)
    Server->>Server: validateJSONRPC()
    Server->>Server: handleToolsList()
    Server-->>Client: JSON-RPC Response (tool list)

    Client->>Server: JSON-RPC Request (tools/call)
    Server->>Server: validateJSONRPC()
    Server->>Tool: execute(params, context)
    Tool-->>Server: MCPToolResult
    Server-->>Client: JSON-RPC Response (result)

    Note over Server,WorkflowMgr: Dependency Injection Pattern
    Server->>StateRepo: Project operations
    Server->>WorkflowMgr: Workflow operations

    Note over Client,Tool: Error Handling
    Client->>Server: Invalid JSON-RPC Request
    Server->>Server: createErrorResponse(-32601)
    Server-->>Client: JSON-RPC Error Response

## Proposed File Changes

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\types\SDDPhase.ts(NEW)

Create the `SDDPhase` enum as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define the enum with values: NEW, REQUIREMENTS, DESIGN, TASKS, EXECUTION, COMPLETED. Export the enum for use throughout the application. This enum represents the different phases of the Spec Driven Development workflow.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\types\Project.ts(NEW)

Create the Project-related type definitions as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define the `Project` interface with id, name, description, currentPhase, createdAt, updatedAt, and metadata fields. Create `ProjectCreate` interface for project creation with name, description, and metadata. Create `ProjectUpdate` interface for project updates with optional name, description, currentPhase, and metadata. Import `SDDPhase` from `./SDDPhase.ts`.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\types\Workflow.ts(NEW)

Create workflow-related type definitions as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define the `PhaseTransition` interface with from, to, validationRequired, and optional prerequisites fields. Import `SDDPhase` from `./SDDPhase.ts`. These types support the workflow management functionality.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\types\MCP.ts(NEW)

Create MCP-related type definitions as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define `MCPToolResult` interface with content array (supporting text, image, resource types), optional isError and progressToken fields. Define `MCPContext` interface with optional projectId, userId, and metadata fields. Define `JSONRPCRequest` interface with jsonrpc version, id, method, and optional params. Define `JSONRPCResponse` interface with jsonrpc version, id, optional result, and optional error object with code, message, and data.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\types\index.ts(NEW)

Create a barrel export file that re-exports all types from the individual type files. Export everything from `./SDDPhase.ts`, `./Project.ts`, `./Workflow.ts`, and `./MCP.ts`. This provides a single import point for all core types and simplifies imports throughout the application.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\interfaces\IStateRepository.ts(NEW)

Create the `IStateRepository` interface exactly as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define methods for project management: createProject, getProject, updateProject, listProjects, deleteProject. Include specification management methods: saveSpecification, getSpecification, listSpecifications. Import required types from `../types` including Project, ProjectCreate, ProjectUpdate, and SDDPhase. This interface defines the contract for persistent state management.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\interfaces\IWorkflowManager.ts(NEW)

Create the `IWorkflowManager` interface exactly as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define methods for workflow management: validateTransition, executeTransition, getCurrentPhase, getAvailableTransitions, getPhaseRequirements. Import required types from `../types` including SDDPhase. This interface defines the contract for managing SDD workflow phase transitions and validations.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\interfaces\IMCPToolHandler.ts(NEW)

Create the `IMCPToolHandler` interface exactly as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Define the interface with name, description, inputSchema properties and execute method that takes params and optional context, returning a Promise of MCPToolResult. Import required types from `../types` including MCPToolResult and MCPContext. This interface defines the contract for MCP tool implementations.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\interfaces\IMCPPromptHandler.ts(NEW)

Create the `IMCPPromptHandler` interface based on the MCP protocol requirements referenced in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/ARCHITECTURE.md`. Define the interface with name, description properties and a render method that takes optional parameters and context, returning a Promise of prompt content. This interface is needed for the MCPServer's prompt handling functionality but wasn't fully specified in the preparation document.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\interfaces\index.ts(NEW)

Create a barrel export file that re-exports all interfaces from the individual interface files. Export everything from `./IStateRepository.ts`, `./IWorkflowManager.ts`, `./IMCPToolHandler.ts`, and `./IMCPPromptHandler.ts`. This provides a single import point for all core interfaces and simplifies imports throughout the application.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\server\MCPServer.ts(NEW)

Create the `MCPServer` class exactly as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Implement the class with private maps for tools and prompts, constructor accepting IStateRepository and IWorkflowManager dependencies. Implement the main `handleJSONRPC` method that routes requests to appropriate handlers (tools/list, tools/call, prompts/list, prompts/get). Include `registerTool` and `registerPrompt` methods. Implement private helper methods: `handleToolsList`, `handleToolsCall`, `handlePromptsList`, `handlePromptsGet`, and `createErrorResponse`. Import required interfaces and types from `../interfaces` and `../types`. Use proper JSON-RPC 2.0 error codes (-32601, -32602, -32603).

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\core\index.ts(NEW)

Create a barrel export file for the core module that re-exports all public interfaces, types, and classes. Export everything from `./interfaces`, `./types`, and the MCPServer class from `./server/MCPServer.ts`. This provides a clean public API for the core module and simplifies imports for other parts of the application.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tsconfig.json(NEW)

Update the TypeScript configuration to enforce strict typing as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Ensure strict mode is enabled, add `noUncheckedIndexedAccess: true`, `noImplicitReturns: true`, `noFallthroughCasesInSwitch: true`, and `exactOptionalPropertyTypes: true` for maximum type safety. Configure `moduleResolution: 'node'`, `allowSyntheticDefaultImports: true`, and `esModuleInterop: true`. Set `target: 'ES2022'` and `module: 'commonjs'` as specified.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\.eslintrc.json(NEW)

Update the ESLint configuration to match the exact specification from `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Configure TypeScript parser, add recommended TypeScript rules, and include specific rules: `@typescript-eslint/no-unused-vars: error`, `@typescript-eslint/no-explicit-any: warn`, `@typescript-eslint/explicit-function-return-type: error`, `@typescript-eslint/no-floating-promises: error`, `prefer-const: error`, `no-var: error`. Set parser options for ES2022 and module source type with project reference to tsconfig.json.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\.prettierrc(NEW)

Update the Prettier configuration to match the exact specification from `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Set `semi: true`, `trailingComma: 'es5'`, `singleQuote: true`, `printWidth: 100`, `tabWidth: 2`, and `useTabs: false`. This ensures consistent code formatting across the project.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\src\index.ts(NEW)

Update the main entry point to export the core module components and provide a basic server instantiation example. Import and re-export the core interfaces, types, and MCPServer class from `./core`. Add a simple example of how to instantiate the MCPServer with stub implementations for testing purposes. This serves as the main entry point for the MCP server application and demonstrates proper usage of the defined interfaces.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\unit\core\types.test.ts(NEW)

Create unit tests for the core types and enums. Test that the `SDDPhase` enum contains all expected values (NEW, REQUIREMENTS, DESIGN, TASKS, EXECUTION, COMPLETED). Create type-level tests to ensure the interfaces are properly shaped and can be implemented. Test that the Project, ProjectCreate, and ProjectUpdate types have the correct properties and types. Verify that MCPToolResult and MCPContext types are properly defined.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\unit\core\MCPServer.test.ts(NEW)

Create comprehensive unit tests for the MCPServer class. Test the constructor with mock dependencies. Test the `handleJSONRPC` method with valid requests for tools/list, tools/call, prompts/list, and prompts/get. Test error handling for invalid methods (should return -32601 error). Test error handling for malformed requests and tool execution failures. Test the `registerTool` and `registerPrompt` methods. Mock the IStateRepository and IWorkflowManager dependencies using Jest mocks.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\integration\mcp-basic.test.ts(NEW)

Create the integration test exactly as specified in `c:/Users/<USER>/Documents/GitHub/Reproduced/Kiro-Spec-SDD/SPECS-MCP/PHASE_0_PREPARATION.md`. Test the MCP JSON-RPC protocol compliance by creating a test Express app that uses the MCPServer. Test tools/list request returns proper JSON-RPC 2.0 response format. Test invalid method returns -32601 error code. Test malformed JSON-RPC requests return appropriate errors. Create a test helper function to set up the Express app with MCPServer integration.

### c:\Users\<USER>\Documents\GitHub\Reproduced\Kiro-Spec-SDD\mcp-sdd-server\tests\helpers\test-app.ts(NEW)

Create a test helper function that sets up an Express application with the MCPServer for integration testing. Create mock implementations of IStateRepository and IWorkflowManager for testing purposes. Set up an Express route that handles POST requests to `/mcp` and forwards them to the MCPServer's handleJSONRPC method. Include proper error handling and JSON parsing. This helper will be used by the integration tests to create a testable HTTP server.